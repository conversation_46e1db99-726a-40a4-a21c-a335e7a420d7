#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/17
@File_Desc: 调解管理URL配置
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views.mediation_plan_view_set import MediationPlanViewSet
from .views.mediation_case_view_set import MediationCaseViewSet
from .views.mediation_case_views import MediationCaseByDebtorView, MediationContentView, MediationPlanConfigView

# 创建主路由器并注册视图集
router = DefaultRouter()
router.register(r'mediation_plan', MediationPlanViewSet, basename='mediation_plan')
router.register(r'mediation_case', MediationCaseViewSet, basename='mediation_case')

# 创建调解案件子路由配置
mediation_case_urlpatterns = [
    # 调解案件按债务人查询接口 - 根据债务人姓名和身份证号统计调解案件数量
    path('by_debtor/', MediationCaseByDebtorView.as_view(), name='mediation_case_by_debtor'),
    # 调解信息内容获取接口 - 根据调解案件ID获取处理后的调解配置信息
    path('<int:mediation_case_id>/content/', MediationContentView.as_view(), name='mediation_content'),
    # 调解方案配置获取接口 - 根据调解案件ID获取相关调解方案的配置信息
    path('<int:mediation_case_id>/plan_config/', MediationPlanConfigView.as_view(), name='mediation_plan_config'),
]

# URL配置
urlpatterns = [
    path('', include(router.urls)),
    # 调解案件相关子路由 - 嵌套在 /mediation_management/mediation_case/ 路径下
    path('mediation_case/', include(mediation_case_urlpatterns)),
]
